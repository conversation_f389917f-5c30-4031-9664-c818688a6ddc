/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: #f5f5f5;
  color: #333;
}

.app {
  min-height: 100vh;
}

/* 导航栏样式 */
.navbar {
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-brand h2 {
  color: #007bff;
  margin: 0;
}

.nav-links {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.nav-links button {
  background: none;
  border: 1px solid #007bff;
  color: #007bff;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.nav-links button:hover {
  background-color: #007bff;
  color: white;
}

/* 主内容区域 */
.main-content {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 2rem;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 2rem;
  font-size: 1.2rem;
  color: #666;
}

/* 首页样式 */
.home-page h1 {
  text-align: center;
  margin-bottom: 2rem;
  color: #333;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.product-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.3s;
}

.product-card:hover {
  transform: translateY(-2px);
}

.product-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.product-card h3 {
  margin-bottom: 0.5rem;
  color: #333;
}

.product-description {
  color: #666;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.product-price {
  font-size: 1.5rem;
  font-weight: bold;
  color: #e74c3c;
  margin-bottom: 1rem;
}

.buy-button {
  width: 100%;
  background-color: #28a745;
  color: white;
  border: none;
  padding: 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s;
}

.buy-button:hover:not(:disabled) {
  background-color: #218838;
}

.buy-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.no-products {
  text-align: center;
  color: #666;
  font-size: 1.2rem;
  padding: 2rem;
}

/* 认证页面样式 */
.auth-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.auth-form {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  width: 100%;
  max-width: 400px;
}

.auth-form h2 {
  text-align: center;
  margin-bottom: 1.5rem;
  color: #333;
}

.auth-form input {
  width: 100%;
  padding: 0.75rem;
  margin-bottom: 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.auth-form input:focus {
  outline: none;
  border-color: #007bff;
}

.checkbox-label {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  cursor: pointer;
}

.checkbox-label input {
  width: auto;
  margin-right: 0.5rem;
  margin-bottom: 0;
}

.auth-form button {
  width: 100%;
  background-color: #007bff;
  color: white;
  border: none;
  padding: 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s;
}

.auth-form button:hover:not(:disabled) {
  background-color: #0056b3;
}

.auth-form button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* 订单页面样式 */
.orders-page h2 {
  margin-bottom: 2rem;
  color: #333;
}

.orders-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.order-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.order-card h3 {
  margin-bottom: 1rem;
  color: #333;
}

.order-card p {
  margin-bottom: 0.5rem;
  color: #666;
}

.pay-button {
  background-color: #ffc107;
  color: #333;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  margin-top: 1rem;
  transition: background-color 0.3s;
}

.pay-button:hover {
  background-color: #e0a800;
}

/* 商家页面样式 */
.seller-page h2 {
  margin-bottom: 2rem;
  color: #333;
}

.seller-actions {
  margin-bottom: 2rem;
}

.add-product-button {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s;
}

.add-product-button:hover {
  background-color: #218838;
}

.my-products h3 {
  margin-bottom: 1rem;
  color: #333;
}

.seller-product {
  border-left: 4px solid #007bff;
}

.product-status {
  font-weight: bold;
  color: #28a745;
}

.digital-content {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.digital-content strong {
  display: block;
  margin-bottom: 0.5rem;
  color: #333;
}

.digital-content p {
  color: #666;
  font-family: monospace;
  background-color: white;
  padding: 0.5rem;
  border-radius: 4px;
  word-break: break-all;
}

/* 添加商品表单样式 */
.add-product-form {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
}

.add-product-form h3 {
  margin-bottom: 1.5rem;
  color: #333;
}

.add-product-form form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.add-product-form input,
.add-product-form textarea {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  font-family: inherit;
}

.add-product-form input:focus,
.add-product-form textarea:focus {
  outline: none;
  border-color: #007bff;
}

.add-product-form textarea {
  min-height: 100px;
  resize: vertical;
}

.add-product-form button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s;
}

.add-product-form button:hover:not(:disabled) {
  background-color: #0056b3;
}

.add-product-form button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .navbar {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .nav-links {
    flex-wrap: wrap;
    justify-content: center;
  }

  .main-content {
    padding: 0 1rem;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .auth-form {
    margin: 1rem;
  }
}
