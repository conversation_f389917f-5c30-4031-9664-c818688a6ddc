<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>虚拟商品商城</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background-color: #f5f5f5;
            color: #333;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .navbar {
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 1rem 3rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-brand h2 {
            color: #007bff;
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }

        .nav-links {
            display: flex;
            gap: 1.5rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .nav-links button {
            background: none;
            border: 2px solid #007bff;
            color: #007bff;
            padding: 0.6rem 1.2rem;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.95rem;
        }

        .nav-links button:hover {
            background-color: #007bff;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,123,255,0.3);
        }

        .main-content {
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 3rem;
            min-height: calc(100vh - 120px);
        }

        .home-page h1 {
            text-align: center;
            margin-bottom: 3rem;
            color: #333;
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #007bff, #0056b3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2.5rem;
            margin-bottom: 3rem;
            padding: 1rem 0;
        }

        .product-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
        }

        .product-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #007bff, #28a745);
        }

        .product-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }

        .product-card h3 {
            margin-bottom: 1rem;
            color: #333;
            font-size: 1.4rem;
            font-weight: 600;
            line-height: 1.3;
        }

        .product-description {
            color: #666;
            margin-bottom: 1.5rem;
            line-height: 1.6;
            font-size: 0.95rem;
            min-height: 3rem;
        }

        .product-price {
            font-size: 1.8rem;
            font-weight: 700;
            color: #e74c3c;
            margin-bottom: 1.5rem;
            text-align: center;
            padding: 0.5rem;
            background: linear-gradient(135deg, #fff5f5, #ffe6e6);
            border-radius: 8px;
            border: 2px solid #ffe6e6;
        }

        .buy-button {
            width: 100%;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .buy-button:hover:not(:disabled) {
            background: linear-gradient(135deg, #218838, #1ea085);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }

        .buy-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .auth-form {
            background: white;
            padding: 3rem;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.12);
            width: 100%;
            max-width: 480px;
            margin: 3rem auto;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .auth-form h2 {
            text-align: center;
            margin-bottom: 2rem;
            color: #333;
            font-size: 1.8rem;
            font-weight: 600;
        }

        .auth-form input, .auth-form textarea {
            width: 100%;
            padding: 1rem;
            margin-bottom: 1.5rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .auth-form input:focus, .auth-form textarea:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }

        .auth-form button {
            width: 100%;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .auth-form button:hover:not(:disabled) {
            background: linear-gradient(135deg, #0056b3, #004085);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,123,255,0.3);
        }

        .auth-form button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .hidden {
            display: none;
        }

        .loading {
            text-align: center;
            padding: 4rem 2rem;
            font-size: 1.3rem;
            color: #666;
            background: white;
            border-radius: 12px;
            margin: 2rem 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .loading::before {
            content: '⏳';
            display: block;
            font-size: 2rem;
            margin-bottom: 1rem;
            animation: spin 2s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .no-products {
            text-align: center;
            color: #666;
            font-size: 1.3rem;
            padding: 4rem 2rem;
            background: white;
            border-radius: 12px;
            margin: 2rem 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .no-products::before {
            content: '📦';
            display: block;
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            cursor: pointer;
        }

        .checkbox-label input {
            width: auto;
            margin-right: 0.5rem;
            margin-bottom: 0;
        }

        .status-message {
            padding: 1.5rem;
            margin: 1.5rem 0;
            border-radius: 12px;
            text-align: center;
            font-weight: 500;
            font-size: 1.1rem;
            position: fixed;
            top: 100px;
            right: 20px;
            max-width: 400px;
            z-index: 1001;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        .success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border: 2px solid #28a745;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.2);
        }

        .success::before {
            content: '✅ ';
            font-size: 1.2rem;
        }

        .error {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border: 2px solid #dc3545;
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.2);
        }

        .error::before {
            content: '❌ ';
            font-size: 1.2rem;
        }

        /* 商家页面样式 */
        .seller-page h2, .orders-page h2 {
            font-size: 2rem;
            margin-bottom: 2rem;
            color: #333;
            text-align: center;
        }

        .seller-actions {
            text-align: center;
            margin-bottom: 3rem;
        }

        .seller-actions button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .seller-actions button:hover {
            background: linear-gradient(135deg, #218838, #1ea085);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }

        /* 订单卡片样式 */
        .order-card {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }

        .order-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .order-card h3 {
            color: #007bff;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .order-card p {
            margin-bottom: 0.8rem;
            color: #666;
            font-size: 1rem;
        }

        .pay-button {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: #333;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin-top: 1rem;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .pay-button:hover {
            background: linear-gradient(135deg, #e0a800, #e8590c);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .main-content {
                max-width: 1000px;
                padding: 0 2rem;
            }

            .products-grid {
                grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
                gap: 2rem;
            }
        }

        @media (max-width: 768px) {
            .navbar {
                flex-direction: column;
                gap: 1rem;
                padding: 1rem 2rem;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
                gap: 1rem;
            }

            .main-content {
                padding: 0 1rem;
                margin: 1rem auto;
            }

            .home-page h1 {
                font-size: 2rem;
                margin-bottom: 2rem;
            }

            .products-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .product-card {
                padding: 1.5rem;
            }

            .auth-form {
                margin: 1rem;
                padding: 2rem;
                max-width: none;
            }

            .status-message {
                position: relative;
                top: auto;
                right: auto;
                max-width: none;
                margin: 1rem 0;
            }
        }

        @media (max-width: 480px) {
            .navbar {
                padding: 1rem;
            }

            .nav-brand h2 {
                font-size: 1.4rem;
            }

            .nav-links button {
                padding: 0.5rem 0.8rem;
                font-size: 0.9rem;
            }

            .main-content {
                padding: 0 0.5rem;
            }

            .home-page h1 {
                font-size: 1.6rem;
            }

            .product-card {
                padding: 1rem;
            }

            .product-price {
                font-size: 1.5rem;
            }

            .auth-form {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-brand">
            <h2>虚拟商品商城</h2>
        </div>
        <div class="nav-links">
            <button onclick="showHome()">首页</button>
            <span id="user-info" class="hidden">
                <button onclick="showOrders()">我的订单</button>
                <button id="seller-btn" onclick="showSeller()" class="hidden">商家中心</button>
                <span id="username"></span>
                <button onclick="logout()">退出</button>
            </span>
            <span id="guest-info">
                <button onclick="showLogin()">登录</button>
                <button onclick="showRegister()">注册</button>
            </span>
        </div>
    </nav>

    <main class="main-content">
        <!-- 首页 -->
        <div id="home-page">
            <h1>虚拟商品商城</h1>
            <div id="products-container">
                <div class="loading">加载商品中...</div>
            </div>
        </div>

        <!-- 登录页面 -->
        <div id="login-page" class="hidden">
            <form class="auth-form" onsubmit="login(event)">
                <h2>登录</h2>
                <input type="text" id="login-username" placeholder="用户名" required>
                <input type="password" id="login-password" placeholder="密码" required>
                <button type="submit">登录</button>
            </form>
        </div>

        <!-- 注册页面 -->
        <div id="register-page" class="hidden">
            <form class="auth-form" onsubmit="register(event)">
                <h2>注册</h2>
                <input type="text" id="register-username" placeholder="用户名" required>
                <input type="email" id="register-email" placeholder="邮箱" required>
                <input type="password" id="register-password" placeholder="密码" required>
                <label class="checkbox-label">
                    <input type="checkbox" id="register-seller">
                    注册为商家
                </label>
                <button type="submit">注册</button>
            </form>
        </div>

        <!-- 订单页面 -->
        <div id="orders-page" class="hidden">
            <h2>我的订单</h2>
            <div id="orders-container">
                <div class="loading">加载订单中...</div>
            </div>
        </div>

        <!-- 商家页面 -->
        <div id="seller-page" class="hidden">
            <h2>商家中心</h2>
            <button onclick="showAddProduct()">添加商品</button>
            <div id="add-product-form" class="hidden">
                <form class="auth-form" onsubmit="addProduct(event)">
                    <h3>添加新商品</h3>
                    <input type="text" id="product-title" placeholder="商品标题" required>
                    <textarea id="product-description" placeholder="商品描述" required style="min-height: 100px; resize: vertical;"></textarea>
                    <input type="number" id="product-price" placeholder="价格" step="0.01" required>
                    <textarea id="product-content" placeholder="虚拟商品内容（激活码、下载链接等）" required style="min-height: 100px; resize: vertical;"></textarea>
                    <input type="url" id="product-image" placeholder="商品图片URL（可选）">
                    <button type="submit">添加商品</button>
                </form>
            </div>
            <div id="my-products">
                <h3>我的商品</h3>
                <div id="my-products-container">
                    <div class="loading">加载商品中...</div>
                </div>
            </div>
        </div>
    </main>

    <script>
        const API_BASE = 'http://localhost:8000';
        let currentUser = null;

        // 页面切换函数
        function showPage(pageId) {
            const pages = ['home-page', 'login-page', 'register-page', 'orders-page', 'seller-page'];
            pages.forEach(id => {
                document.getElementById(id).classList.add('hidden');
            });
            document.getElementById(pageId).classList.remove('hidden');
        }

        function showHome() { showPage('home-page'); loadProducts(); }
        function showLogin() { showPage('login-page'); }
        function showRegister() { showPage('register-page'); }
        function showOrders() { showPage('orders-page'); loadOrders(); }
        function showSeller() { showPage('seller-page'); loadMyProducts(); }

        function showAddProduct() {
            const form = document.getElementById('add-product-form');
            form.classList.toggle('hidden');
        }

        // API请求函数
        async function apiRequest(endpoint, options = {}) {
            const token = localStorage.getItem('token');
            const config = {
                headers: {
                    'Content-Type': 'application/json',
                    ...(token && { Authorization: `Bearer ${token}` }),
                },
                ...options,
            };

            if (config.body && typeof config.body === 'object') {
                config.body = JSON.stringify(config.body);
            }

            const response = await fetch(`${API_BASE}${endpoint}`, config);
            
            if (!response.ok) {
                const error = await response.json().catch(() => ({ detail: 'Network error' }));
                throw new Error(error.detail || 'Request failed');
            }
            
            return response.json();
        }

        // 用户认证
        async function login(event) {
            event.preventDefault();
            const username = document.getElementById('login-username').value;
            const password = document.getElementById('login-password').value;

            try {
                const formData = new FormData();
                formData.append('username', username);
                formData.append('password', password);

                const response = await fetch(`${API_BASE}/api/users/token`, {
                    method: 'POST',
                    body: formData,
                });

                if (!response.ok) {
                    throw new Error('登录失败');
                }

                const data = await response.json();
                localStorage.setItem('token', data.access_token);
                
                const userData = await apiRequest('/api/users/me');
                setCurrentUser(userData);
                showHome();
                showMessage('登录成功！', 'success');
            } catch (error) {
                showMessage('登录失败: ' + error.message, 'error');
            }
        }

        async function register(event) {
            event.preventDefault();
            const username = document.getElementById('register-username').value;
            const email = document.getElementById('register-email').value;
            const password = document.getElementById('register-password').value;
            const is_seller = document.getElementById('register-seller').checked;

            try {
                await apiRequest('/api/users/register', {
                    method: 'POST',
                    body: { username, email, password, is_seller }
                });
                showMessage('注册成功！请登录', 'success');
                showLogin();
            } catch (error) {
                showMessage('注册失败: ' + error.message, 'error');
            }
        }

        function logout() {
            localStorage.removeItem('token');
            currentUser = null;
            updateUI();
            showHome();
            showMessage('已退出登录', 'success');
        }

        function setCurrentUser(user) {
            currentUser = user;
            updateUI();
        }

        function updateUI() {
            const userInfo = document.getElementById('user-info');
            const guestInfo = document.getElementById('guest-info');
            const sellerBtn = document.getElementById('seller-btn');
            const username = document.getElementById('username');

            if (currentUser) {
                userInfo.classList.remove('hidden');
                guestInfo.classList.add('hidden');
                username.textContent = `欢迎, ${currentUser.username}`;
                
                if (currentUser.is_seller) {
                    sellerBtn.classList.remove('hidden');
                } else {
                    sellerBtn.classList.add('hidden');
                }
            } else {
                userInfo.classList.add('hidden');
                guestInfo.classList.remove('hidden');
            }
        }

        // 商品相关
        async function loadProducts() {
            try {
                const products = await apiRequest('/api/products/');
                displayProducts(products);
            } catch (error) {
                document.getElementById('products-container').innerHTML = 
                    '<div class="no-products">加载商品失败</div>';
            }
        }

        function displayProducts(products) {
            const container = document.getElementById('products-container');
            
            if (products.length === 0) {
                container.innerHTML = '<div class="no-products">暂无商品</div>';
                return;
            }

            const html = `
                <div class="products-grid">
                    ${products.map(product => `
                        <div class="product-card">
                            ${product.image_url ? `
                                <div style="position: relative; margin-bottom: 1.5rem;">
                                    <img src="${product.image_url}" alt="${product.title}"
                                         style="width: 100%; height: 220px; object-fit: cover; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
                                    <div style="position: absolute; top: 10px; right: 10px; background: rgba(255,255,255,0.9); padding: 0.3rem 0.6rem; border-radius: 20px; font-size: 0.8rem; font-weight: 600; color: #28a745;">
                                        🔥 热销
                                    </div>
                                </div>
                            ` : `
                                <div style="width: 100%; height: 220px; background: linear-gradient(135deg, #f8f9fa, #e9ecef); border-radius: 8px; margin-bottom: 1.5rem; display: flex; align-items: center; justify-content: center; color: #6c757d; font-size: 3rem;">
                                    📦
                                </div>
                            `}
                            <h3>${product.title}</h3>
                            <p class="product-description">${product.description}</p>
                            <div class="product-price">¥${product.price}</div>
                            <button class="buy-button" onclick="purchaseProduct(${product.id})" ${!currentUser ? 'disabled' : ''}>
                                ${currentUser ? '🛒 立即购买' : '🔒 请先登录'}
                            </button>
                        </div>
                    `).join('')}
                </div>
            `;
            
            container.innerHTML = html;
        }

        async function purchaseProduct(productId) {
            if (!currentUser) {
                showMessage('请先登录', 'error');
                return;
            }

            try {
                const orderData = {
                    items: [{ product_id: productId, quantity: 1 }]
                };
                
                const order = await apiRequest('/api/orders/', {
                    method: 'POST',
                    body: orderData
                });
                
                showMessage(`订单创建成功！订单号: ${order.id}`, 'success');
                
                // 模拟支付
                if (confirm('是否立即支付？')) {
                    await apiRequest(`/api/orders/${order.id}/pay`, { method: 'POST' });
                    showMessage('支付成功！虚拟商品将发送到您的邮箱', 'success');
                }
            } catch (error) {
                showMessage('购买失败: ' + error.message, 'error');
            }
        }

        // 商家功能
        async function addProduct(event) {
            event.preventDefault();
            
            const productData = {
                title: document.getElementById('product-title').value,
                description: document.getElementById('product-description').value,
                price: parseFloat(document.getElementById('product-price').value),
                digital_content: document.getElementById('product-content').value,
                image_url: document.getElementById('product-image').value || null
            };

            try {
                await apiRequest('/api/products/', {
                    method: 'POST',
                    body: productData
                });
                
                showMessage('商品添加成功！', 'success');
                document.getElementById('add-product-form').classList.add('hidden');
                loadMyProducts();
                loadProducts(); // 刷新首页商品列表
                
                // 重置表单
                event.target.reset();
            } catch (error) {
                showMessage('添加商品失败: ' + error.message, 'error');
            }
        }

        async function loadMyProducts() {
            try {
                const products = await apiRequest('/api/products/my');
                displayMyProducts(products);
            } catch (error) {
                document.getElementById('my-products-container').innerHTML = 
                    '<div class="no-products">加载商品失败</div>';
            }
        }

        function displayMyProducts(products) {
            const container = document.getElementById('my-products-container');
            
            if (products.length === 0) {
                container.innerHTML = '<div class="no-products">暂无商品</div>';
                return;
            }

            const html = `
                <div class="products-grid">
                    ${products.map(product => `
                        <div class="product-card" style="border-left: 6px solid #007bff; position: relative;">
                            <div style="position: absolute; top: 1rem; right: 1rem; background: ${product.is_active ? 'linear-gradient(135deg, #28a745, #20c997)' : 'linear-gradient(135deg, #6c757d, #5a6268)'}; color: white; padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.8rem; font-weight: 600;">
                                ${product.is_active ? '✅ 上架中' : '⏸️ 已下架'}
                            </div>
                            <h4 style="font-size: 1.3rem; margin-bottom: 1rem; color: #007bff; padding-right: 5rem;">${product.title}</h4>
                            <p style="color: #666; margin-bottom: 1rem; line-height: 1.5;">${product.description}</p>
                            <div class="product-price" style="margin-bottom: 1.5rem;">¥${product.price}</div>
                            <div style="background: linear-gradient(135deg, #f8f9fa, #e9ecef); padding: 1.5rem; border-radius: 12px; border: 2px solid #dee2e6;">
                                <div style="display: flex; align-items: center; margin-bottom: 1rem;">
                                    <span style="font-size: 1.2rem; margin-right: 0.5rem;">🎁</span>
                                    <strong style="color: #495057; font-size: 1.1rem;">虚拟商品内容</strong>
                                </div>
                                <div style="background: white; padding: 1rem; border-radius: 8px; border: 1px solid #ced4da; box-shadow: inset 0 2px 4px rgba(0,0,0,0.05);">
                                    <code style="color: #e83e8c; font-family: 'Courier New', monospace; font-size: 0.9rem; word-break: break-all; line-height: 1.4;">${product.digital_content}</code>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
            
            container.innerHTML = html;
        }

        // 订单功能
        async function loadOrders() {
            try {
                const orders = await apiRequest('/api/orders/');
                displayOrders(orders);
            } catch (error) {
                document.getElementById('orders-container').innerHTML = 
                    '<div class="no-products">加载订单失败</div>';
            }
        }

        function displayOrders(orders) {
            const container = document.getElementById('orders-container');
            
            if (orders.length === 0) {
                container.innerHTML = '<div class="no-products">暂无订单</div>';
                return;
            }

            const statusMap = {
                'pending': '待支付',
                'paid': '已支付',
                'delivered': '已发货',
                'cancelled': '已取消'
            };

            const html = orders.map(order => `
                <div class="order-card">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                        <h3 style="margin: 0; color: #007bff; font-size: 1.4rem;">📋 订单 #${order.id}</h3>
                        <div style="background: ${getStatusColor(order.status)}; color: white; padding: 0.4rem 1rem; border-radius: 20px; font-size: 0.9rem; font-weight: 600;">
                            ${getStatusIcon(order.status)} ${statusMap[order.status] || order.status}
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1.5rem;">
                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px;">
                            <div style="color: #6c757d; font-size: 0.9rem; margin-bottom: 0.3rem;">💰 订单金额</div>
                            <div style="font-size: 1.3rem; font-weight: bold; color: #e74c3c;">¥${order.total_amount}</div>
                        </div>
                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px;">
                            <div style="color: #6c757d; font-size: 0.9rem; margin-bottom: 0.3rem;">📅 创建时间</div>
                            <div style="font-weight: 500; color: #495057;">${new Date(order.created_at).toLocaleString()}</div>
                        </div>
                        ${order.products ? `
                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px;">
                            <div style="color: #6c757d; font-size: 0.9rem; margin-bottom: 0.3rem;">🛍️ 商品</div>
                            <div style="font-weight: 500; color: #495057;">${order.products}</div>
                        </div>
                        ` : ''}
                    </div>

                    ${order.status === 'pending' ? `
                        <button onclick="payOrder(${order.id})" class="pay-button">
                            💳 立即支付 ¥${order.total_amount}
                        </button>
                    ` : order.status === 'paid' ? `
                        <div style="background: linear-gradient(135deg, #d4edda, #c3e6cb); padding: 1rem; border-radius: 8px; text-align: center; color: #155724; font-weight: 600;">
                            ✅ 支付完成，虚拟商品已发货到您的邮箱
                        </div>
                    ` : ''}
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        async function payOrder(orderId) {
            try {
                await apiRequest(`/api/orders/${orderId}/pay`, { method: 'POST' });
                showMessage('支付成功！虚拟商品将发送到您的邮箱', 'success');
                loadOrders();
            } catch (error) {
                showMessage('支付失败: ' + error.message, 'error');
            }
        }

        // 辅助函数
        function getStatusColor(status) {
            const colorMap = {
                'pending': 'linear-gradient(135deg, #ffc107, #fd7e14)',
                'paid': 'linear-gradient(135deg, #28a745, #20c997)',
                'delivered': 'linear-gradient(135deg, #007bff, #0056b3)',
                'cancelled': 'linear-gradient(135deg, #dc3545, #c82333)'
            };
            return colorMap[status] || 'linear-gradient(135deg, #6c757d, #5a6268)';
        }

        function getStatusIcon(status) {
            const iconMap = {
                'pending': '⏳',
                'paid': '✅',
                'delivered': '🚚',
                'cancelled': '❌'
            };
            return iconMap[status] || '📋';
        }

        // 消息显示
        function showMessage(message, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `status-message ${type}`;
            messageDiv.textContent = message;
            
            document.body.appendChild(messageDiv);
            
            setTimeout(() => {
                document.body.removeChild(messageDiv);
            }, 3000);
        }

        // 页面加载时初始化
        window.onload = function() {
            const token = localStorage.getItem('token');
            if (token) {
                apiRequest('/api/users/me')
                    .then(userData => setCurrentUser(userData))
                    .catch(() => localStorage.removeItem('token'));
            }
            loadProducts();
        };
    </script>
</body>
</html>
