# 虚拟商品商城

一个支持虚拟商品发布和自动发货的电商平台，使用Python FastAPI后端和React前端。

## 功能特性

- 🔐 用户注册登录系统
- 🛍️ 商品浏览和购买
- 💰 模拟支付系统
- 📧 自动邮件发货
- 👨‍💼 商家管理后台
- 📱 响应式设计

## 技术栈

### 后端
- **FastAPI** - 现代Python Web框架
- **SQLAlchemy** - ORM数据库操作
- **SQLite** - 轻量级数据库
- **JWT** - 用户认证
- **SMTP** - 邮件发送

### 前端
- **React** - 用户界面框架
- **Vite** - 构建工具
- **CSS3** - 样式设计

## 快速开始

### 方法一：使用启动脚本（推荐）

1. 双击运行 `start.bat` 文件
2. 等待依赖安装和服务启动
3. 访问 http://localhost:5173 使用应用

### 方法二：简化启动（推荐，解决Node.js版本问题）

如果遇到Node.js版本过低的问题（如 `crypto$2.getRandomValues is not a function`），可以使用简化版：

```bash
# 启动简化版后端（使用Python内置模块）
cd backend
py start_simple.py

# 使用简化HTML前端（无需Node.js）
# 直接打开 frontend_simple.html 文件
```

### 方法三：手动启动

#### 启动后端

```bash
cd backend
py -m pip install -r requirements.txt
py main.py
```

#### 启动前端

```bash
cd frontend
npm install
npm run dev
```

## 访问地址

- **简化HTML前端**: 直接打开 `frontend_simple.html` 文件
- **React前端**: http://localhost:5173 (需要Node.js 18+)
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **测试页面**: 直接打开 `test.html` 文件

## ⚠️ 常见问题解决

### Node.js版本问题

如果遇到以下错误：
```
TypeError: crypto$2.getRandomValues is not a function
```

**原因**: 您的Node.js版本过低（当前v16.20.2），Vite 6需要Node.js 18+

**解决方案**:
1. **推荐**: 使用简化HTML前端 (`frontend_simple.html`)，功能完整且无需Node.js
2. 升级Node.js到18+版本: https://nodejs.org/
3. 或者使用降级版本的React前端（已在package.json中配置）

## 使用说明

### 1. 注册账户

- 访问首页，点击"注册"
- 填写用户名、邮箱、密码
- 选择是否注册为商家

### 2. 商家发布商品

- 以商家身份登录
- 进入"商家中心"
- 点击"添加商品"
- 填写商品信息和虚拟商品内容

### 3. 客户购买商品

- 浏览商品列表
- 点击"立即购买"
- 完成支付流程
- 虚拟商品将自动发送到邮箱

## 配置说明

### 邮件配置

编辑 `backend/services/email_service.py` 文件：

```python
self.smtp_server = "smtp.gmail.com"  # 邮件服务器
self.sender_email = "<EMAIL>"  # 发送邮箱
self.sender_password = "your-app-password"  # 应用密码
```

### 安全配置

编辑 `backend/auth.py` 文件：

```python
SECRET_KEY = "your-secret-key-here"  # JWT密钥
```

## 项目结构

```
super_resource/
├── backend/                 # 后端代码
│   ├── main.py             # FastAPI应用入口
│   ├── models.py           # 数据库模型
│   ├── database.py         # 数据库配置
│   ├── auth.py             # 认证模块
│   ├── routes/             # API路由
│   │   ├── users.py        # 用户相关API
│   │   ├── products.py     # 商品相关API
│   │   ├── orders.py       # 订单相关API
│   │   └── payments.py     # 支付相关API
│   └── services/           # 业务服务
│       ├── email_service.py    # 邮件服务
│       └── payment_service.py  # 支付服务
├── frontend/               # 前端代码
│   ├── src/
│   │   ├── App.jsx         # 主应用组件
│   │   └── App.css         # 样式文件
│   └── package.json        # 前端依赖
├── start.bat               # 启动脚本
└── README.md               # 项目说明
```

## 注意事项

1. **邮件配置**: 需要配置真实的SMTP服务器才能发送邮件
2. **支付系统**: 当前为模拟支付，生产环境需集成真实支付接口
3. **安全性**: 生产环境需要更强的密钥和HTTPS
4. **数据库**: 可以轻松迁移到PostgreSQL或MySQL

## 开发计划

- [ ] 集成真实支付接口（支付宝、微信支付）
- [ ] 添加商品分类和搜索功能
- [ ] 实现订单退款功能
- [ ] 添加用户评价系统
- [ ] 优化邮件模板
- [ ] 添加管理员后台

## 许可证

MIT License
