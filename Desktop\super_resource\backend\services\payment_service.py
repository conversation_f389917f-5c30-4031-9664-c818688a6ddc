import uuid
import time
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)

class PaymentService:
    """模拟支付服务 - 在实际项目中应该集成真实的支付接口如支付宝、微信支付等"""
    
    def __init__(self):
        # 模拟支付记录
        self.payments = {}
    
    def create_payment(self, order_id: int, amount: float, user_email: str) -> Dict[str, Any]:
        """创建支付订单"""
        payment_id = str(uuid.uuid4())
        
        payment_data = {
            "payment_id": payment_id,
            "order_id": order_id,
            "amount": amount,
            "user_email": user_email,
            "status": "pending",
            "created_at": time.time(),
            "payment_url": f"http://localhost:8000/payment/{payment_id}"  # 模拟支付页面
        }
        
        self.payments[payment_id] = payment_data
        
        logger.info(f"Payment created: {payment_id} for order {order_id}")
        
        return {
            "payment_id": payment_id,
            "payment_url": payment_data["payment_url"],
            "amount": amount,
            "status": "pending"
        }
    
    def process_payment(self, payment_id: str) -> Dict[str, Any]:
        """处理支付 - 模拟支付成功"""
        if payment_id not in self.payments:
            return {"success": False, "message": "Payment not found"}
        
        payment = self.payments[payment_id]
        
        # 模拟支付处理时间
        time.sleep(1)
        
        # 模拟支付成功（在实际项目中这里会调用真实的支付接口）
        payment["status"] = "completed"
        payment["completed_at"] = time.time()
        
        logger.info(f"Payment processed successfully: {payment_id}")
        
        return {
            "success": True,
            "payment_id": payment_id,
            "status": "completed",
            "order_id": payment["order_id"]
        }
    
    def get_payment_status(self, payment_id: str) -> Dict[str, Any]:
        """获取支付状态"""
        if payment_id not in self.payments:
            return {"success": False, "message": "Payment not found"}
        
        payment = self.payments[payment_id]
        
        return {
            "success": True,
            "payment_id": payment_id,
            "status": payment["status"],
            "order_id": payment["order_id"],
            "amount": payment["amount"]
        }
    
    def refund_payment(self, payment_id: str) -> Dict[str, Any]:
        """退款处理"""
        if payment_id not in self.payments:
            return {"success": False, "message": "Payment not found"}
        
        payment = self.payments[payment_id]
        
        if payment["status"] != "completed":
            return {"success": False, "message": "Payment not completed, cannot refund"}
        
        payment["status"] = "refunded"
        payment["refunded_at"] = time.time()
        
        logger.info(f"Payment refunded: {payment_id}")
        
        return {
            "success": True,
            "payment_id": payment_id,
            "status": "refunded",
            "message": "Refund processed successfully"
        }

# 全局支付服务实例
payment_service = PaymentService()
