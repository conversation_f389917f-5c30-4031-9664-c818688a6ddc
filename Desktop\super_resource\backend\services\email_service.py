import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import List
import logging

logger = logging.getLogger(__name__)

class EmailService:
    def __init__(self):
        # 邮件配置 - 在生产环境中应该使用环境变量
        self.smtp_server = "smtp.gmail.com"  # 可以改为其他邮件服务商
        self.smtp_port = 587
        self.sender_email = "<EMAIL>"  # 替换为实际邮箱
        self.sender_password = "your-app-password"  # 替换为应用密码
    
    def send_digital_product_email(self, recipient_email: str, product_title: str, digital_content: str, order_id: int):
        """发送虚拟商品邮件"""
        try:
            msg = MIMEMultipart()
            msg['From'] = self.sender_email
            msg['To'] = recipient_email
            msg['Subject'] = f"您的虚拟商品已发货 - {product_title}"
            
            # 邮件正文
            body = f"""
            亲爱的客户，
            
            感谢您的购买！您的虚拟商品已经准备就绪。
            
            订单号: {order_id}
            商品名称: {product_title}
            
            商品内容:
            {digital_content}
            
            如有任何问题，请联系客服。
            
            祝您使用愉快！
            虚拟商品商城
            """
            
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # 发送邮件
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.sender_email, self.sender_password)
            text = msg.as_string()
            server.sendmail(self.sender_email, recipient_email, text)
            server.quit()
            
            logger.info(f"Digital product email sent successfully to {recipient_email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email to {recipient_email}: {str(e)}")
            return False
    
    def send_order_confirmation_email(self, recipient_email: str, order_id: int, total_amount: float):
        """发送订单确认邮件"""
        try:
            msg = MIMEMultipart()
            msg['From'] = self.sender_email
            msg['To'] = recipient_email
            msg['Subject'] = f"订单确认 - 订单号 {order_id}"
            
            body = f"""
            亲爱的客户，
            
            您的订单已确认！
            
            订单号: {order_id}
            订单金额: ¥{total_amount}
            
            我们将在收到付款后立即为您发货。
            
            谢谢您的购买！
            虚拟商品商城
            """
            
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.sender_email, self.sender_password)
            text = msg.as_string()
            server.sendmail(self.sender_email, recipient_email, text)
            server.quit()
            
            logger.info(f"Order confirmation email sent successfully to {recipient_email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send order confirmation email to {recipient_email}: {str(e)}")
            return False

# 全局邮件服务实例
email_service = EmailService()
